# CNC Color Customization Feature

## 概要

PrusaSlicerのG-codeビューアーにCNCツールパス用の色カスタマイズ機能を追加しました。この機能により、CNCファイルのツールパス種類別に色を設定し、視覚的に区別することができます。

## 実装された機能

### 1. CNC専用色設定データ構造

- `CNCColorSettings`構造体を新規追加
- 以下の色設定をサポート：
  - **Travels（移動）**: 青色（デフォルト）
  - **Cuts（切削）**: 赤色（デフォルト）
  - **Tool Changes（ツール変更）**: 黄色（デフォルト）
  - **Retractions（リトラクション）**: マゼンタ色（デフォルト）
- 個別色使用フラグ（`use_individual_colors`）

### 2. ユーザーインターフェース

- ファイル管理パネルにCNC専用の色設定UIを追加
- CNCファイルが読み込まれた場合のみ表示
- 以下のコントロールを提供：
  - **Use Individual Colors**: 個別色設定の有効/無効切り替え
  - **色選択ボタン**: 各ツールパス種類の色を個別に設定
  - **Reset to Default**: デフォルト色に戻すボタン

### 3. 色設定の永続化

- AppConfigを使用した設定の保存・復元
- アプリケーション再起動後も色設定が維持される
- 色変更時の自動保存機能

### 4. リアルタイム色変更

- 色選択時の即座な表示更新
- レイヤー表示時の色永続化
- ファイル切り替え時の色設定維持

## 使用方法

### 1. CNCファイルの読み込み

1. PrusaSlicerを起動
2. CNC G-codeファイルを読み込み
3. ファイルが自動的にCNCタイプとして認識される

### 2. 色設定のカスタマイズ

1. ファイル管理パネルを開く
2. CNCファイルの項目で「Use Individual Colors」をチェック
3. 各ツールパス種類の色ボタンをクリックして色を選択
4. 変更は即座に表示に反映される

### 3. 設定のリセット

- 「Reset to Default」ボタンでデフォルト色に戻す
- 「Use Individual Colors」のチェックを外すとデフォルト表示に戻る

## 技術仕様

### ファイル構造

- **GCodeViewer.hpp**: CNCColorSettings構造体とメソッド宣言
- **GCodeViewer.cpp**: 実装コード
- **GCodeProcessor.hpp/cpp**: CNCファイル判定機能

### 主要メソッド

```cpp
// CNC色設定の取得・設定
const CNCColorSettings* get_cnc_colors(int file_id) const;
void set_cnc_colors(int file_id, const CNCColorSettings& cnc_colors);

// 色設定の永続化
void save_cnc_colors_to_config();
void load_cnc_colors_from_config();

// 色適用とレンダリング
void apply_cnc_colors_to_viewer(libvgcode::Viewer& viewer, const CNCColorSettings& cnc_colors);
bool render_cnc_color_settings(GCodeFileData* file_data);
```

### 設定ファイル形式

AppConfigの`[cnc_colors]`セクションに以下の形式で保存：

```ini
[cnc_colors]
use_individual_colors = 1
travels_color = #0080FFFF
cuts_color = #FF0000FF
tool_changes_color = #FFFF00FF
retractions_color = #FF00FFFF
```

## テスト方法

### 自動テスト

```bash
# テストプログラムのコンパイルと実行
g++ -I. test_cnc_color_functionality.cpp -o test_cnc_colors
./test_cnc_colors
```

### 手動テスト

1. **基本機能テスト**
   - CNCファイルの読み込み
   - 色設定UIの表示確認
   - 色変更の動作確認

2. **永続化テスト**
   - 色設定の変更
   - アプリケーション再起動
   - 設定の復元確認

3. **レンダリングテスト**
   - レイヤー表示での色維持
   - ファイル切り替え時の色維持
   - 複数ファイル表示での個別色設定

## 既知の制限事項

1. **libvgcode依存**: libvgcodeライブラリのAPIに依存するため、ライブラリの更新時に調整が必要な場合があります

2. **CNCファイル判定**: G-codeの内容に基づく自動判定のため、特殊なCNCファイルでは手動での種別設定が必要な場合があります

3. **色設定の粒度**: 現在は主要なツールパス種類のみサポート。より細かい分類が必要な場合は拡張が必要です

## 今後の拡張予定

1. **色プリセット機能**: よく使用される色の組み合わせをプリセットとして保存
2. **ツールパス種類の拡張**: より詳細なCNC操作種類への対応
3. **視覚効果の追加**: 線の太さや透明度の調整機能
4. **エクスポート機能**: 色設定の共有・インポート機能

## トラブルシューティング

### 色設定が保存されない場合

1. AppConfigファイルの書き込み権限を確認
2. 設定ディレクトリの存在を確認
3. ログファイルでエラーメッセージを確認

### 色が正しく表示されない場合

1. CNCファイルが正しく認識されているか確認
2. 「Use Individual Colors」がチェックされているか確認
3. libvgcodeライブラリのバージョンを確認

### UIが表示されない場合

1. CNCファイルが読み込まれているか確認
2. ファイル管理パネルが開いているか確認
3. ImGuiライブラリの初期化状態を確認
